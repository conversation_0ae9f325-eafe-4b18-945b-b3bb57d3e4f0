# US-01: User Registration - Flask Application Setup
# This is the main Flask application file for US-01

from flask import Flask
from flask_cors import CORS
from us01_user_model import db
from us01_auth_routes import auth_bp
import os

def create_app():
    """
    Application factory pattern for creating Flask app
    
    Returns:
        Flask: Configured Flask application instance
    """
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
        'DATABASE_URL', 
        'postgresql://username:password@localhost:5432/resume_scanner'
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=['http://localhost:4200'])  # Allow Angular frontend
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    return app

def init_database(app):
    """
    Initialize database with tables
    
    Args:
        app (Flask): Flask application instance
    """
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")

if __name__ == '__main__':
    app = create_app()
    
    # Development server configuration
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
