# US-01: User Registration - Authentication Routes
# This file contains the registration endpoint and related authentication logic

from flask import Blueprint, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from us01_user_model import User, db
import re

# Create Blueprint for authentication routes
auth_bp = Blueprint('auth', __name__, url_prefix='/api')

def validate_email(email):
    """
    Validate email format using regex
    
    Args:
        email (str): Email address to validate
        
    Returns:
        bool: True if email format is valid, False otherwise
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """
    Validate password strength
    Requirements: At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    
    Args:
        password (str): Password to validate
        
    Returns:
        tuple: (bool, str) - (is_valid, error_message)
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    
    return True, ""

@auth_bp.route('/register', methods=['POST'])
def register():
    """
    User registration endpoint
    
    Expected JSON payload:
    {
        "email": "<EMAIL>",
        "password": "SecurePass123"
    }
    
    Returns:
        JSON response with success/error message and status code
    """
    try:
        # Get JSON data from request
        data = request.get_json()
        
        # Validate required fields
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format'
            }), 400
        
        # Validate password strength
        is_valid_password, password_error = validate_password(password)
        if not is_valid_password:
            return jsonify({
                'success': False,
                'message': password_error
            }), 400
        
        # Check if user already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            return jsonify({
                'success': False,
                'message': 'Email already registered'
            }), 409
        
        # Create new user
        new_user = User(email=email, password=password)
        
        # Save to database
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': new_user.to_dict()
        }), 201
        
    except Exception as e:
        # Rollback database changes on error
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Registration failed: {str(e)}'
        }), 500

@auth_bp.route('/check-email', methods=['POST'])
def check_email():
    """
    Check if email is already registered (for frontend validation)
    
    Expected JSON payload:
    {
        "email": "<EMAIL>"
    }
    
    Returns:
        JSON response indicating if email exists
    """
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()
        
        if not email:
            return jsonify({
                'success': False,
                'message': 'Email is required'
            }), 400
        
        existing_user = User.query.filter_by(email=email).first()
        
        return jsonify({
            'success': True,
            'exists': existing_user is not None
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error checking email: {str(e)}'
        }), 500
