# US-01: User Registration - User Model
# This file defines the User model for PostgreSQL database

from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    """
    User model for storing user registration data
    
    Attributes:
        id: Primary key, auto-incrementing integer
        email: User's email address (unique)
        password_hash: Hashed password for security
        created_at: Timestamp when user registered
        is_premium: Boolean flag for premium access (default: False)
    """
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_premium = db.Column(db.<PERSON>, default=False)
    
    def __init__(self, email, password):
        """
        Initialize a new user with email and password
        
        Args:
            email (str): User's email address
            password (str): Plain text password (will be hashed)
        """
        self.email = email
        self.set_password(password)
    
    def set_password(self, password):
        """
        Hash and store the password
        
        Args:
            password (str): Plain text password to hash
        """
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """
        Check if provided password matches stored hash
        
        Args:
            password (str): Plain text password to verify
            
        Returns:
            bool: True if password matches, False otherwise
        """
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """
        Convert user object to dictionary for JSON responses
        
        Returns:
            dict: User data without sensitive information
        """
        return {
            'id': self.id,
            'email': self.email,
            'created_at': self.created_at.isoformat(),
            'is_premium': self.is_premium
        }
    
    def __repr__(self):
        return f'<User {self.email}>'
