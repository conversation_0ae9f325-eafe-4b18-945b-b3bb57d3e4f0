// US-01: User Registration - Frontend JavaScript
// This file handles the registration form logic and API communication

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// DOM Elements
const registerForm = document.getElementById('registerForm');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const confirmPasswordInput = document.getElementById('confirmPassword');
const submitBtn = document.getElementById('submitBtn');
const submitText = document.getElementById('submitText');
const loadingSpinner = document.getElementById('loadingSpinner');
const togglePasswordBtn = document.getElementById('togglePassword');
const eyeIcon = document.getElementById('eyeIcon');

// Error message elements
const emailError = document.getElementById('emailError');
const passwordError = document.getElementById('passwordError');
const confirmPasswordError = document.getElementById('confirmPasswordError');
const successMessage = document.getElementById('successMessage');
const errorMessage = document.getElementById('errorMessage');
const successText = document.getElementById('successText');
const errorText = document.getElementById('errorText');

// Validation functions
function validateEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    const minLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    
    return {
        isValid: minLength && hasUppercase && hasLowercase && hasNumber,
        errors: {
            minLength,
            hasUppercase,
            hasLowercase,
            hasNumber
        }
    };
}

// UI Helper functions
function showError(element, message) {
    element.textContent = message;
    element.classList.remove('hidden');
    element.parentElement.querySelector('input').classList.add('border-red-500');
}

function hideError(element) {
    element.classList.add('hidden');
    element.parentElement.querySelector('input').classList.remove('border-red-500');
}

function showMessage(isSuccess, message) {
    hideMessage();
    
    if (isSuccess) {
        successText.textContent = message;
        successMessage.classList.remove('hidden');
    } else {
        errorText.textContent = message;
        errorMessage.classList.remove('hidden');
    }
}

function hideMessage() {
    successMessage.classList.add('hidden');
    errorMessage.classList.add('hidden');
}

function setLoading(loading) {
    submitBtn.disabled = loading;
    
    if (loading) {
        submitText.textContent = 'Creating Account...';
        loadingSpinner.classList.remove('hidden');
    } else {
        submitText.textContent = 'Create Account';
        loadingSpinner.classList.add('hidden');
    }
}

// Event Listeners
togglePasswordBtn.addEventListener('click', function() {
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    
    if (type === 'text') {
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
});

// Real-time validation
emailInput.addEventListener('blur', function() {
    const email = this.value.trim();
    
    if (email && !validateEmail(email)) {
        showError(emailError, 'Please enter a valid email address');
    } else {
        hideError(emailError);
    }
});

passwordInput.addEventListener('input', function() {
    const password = this.value;
    const validation = validatePassword(password);
    
    if (password && !validation.isValid) {
        let errorMsg = 'Password must contain: ';
        const missing = [];
        
        if (!validation.errors.minLength) missing.push('8+ characters');
        if (!validation.errors.hasUppercase) missing.push('uppercase letter');
        if (!validation.errors.hasLowercase) missing.push('lowercase letter');
        if (!validation.errors.hasNumber) missing.push('number');
        
        errorMsg += missing.join(', ');
        showError(passwordError, errorMsg);
    } else {
        hideError(passwordError);
    }
});

confirmPasswordInput.addEventListener('blur', function() {
    const password = passwordInput.value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        showError(confirmPasswordError, 'Passwords do not match');
    } else {
        hideError(confirmPasswordError);
    }
});

// Form submission
registerForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Clear previous messages
    hideMessage();
    
    // Get form data
    const email = emailInput.value.trim().toLowerCase();
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    
    // Validate all fields
    let hasErrors = false;
    
    if (!email) {
        showError(emailError, 'Email is required');
        hasErrors = true;
    } else if (!validateEmail(email)) {
        showError(emailError, 'Please enter a valid email address');
        hasErrors = true;
    } else {
        hideError(emailError);
    }
    
    if (!password) {
        showError(passwordError, 'Password is required');
        hasErrors = true;
    } else if (!validatePassword(password).isValid) {
        showError(passwordError, 'Password does not meet requirements');
        hasErrors = true;
    } else {
        hideError(passwordError);
    }
    
    if (!confirmPassword) {
        showError(confirmPasswordError, 'Please confirm your password');
        hasErrors = true;
    } else if (password !== confirmPassword) {
        showError(confirmPasswordError, 'Passwords do not match');
        hasErrors = true;
    } else {
        hideError(confirmPasswordError);
    }
    
    if (hasErrors) {
        return;
    }
    
    // Submit registration
    try {
        setLoading(true);
        
        const response = await fetch(`${API_BASE_URL}/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage(true, 'Account created successfully! You can now log in.');
            registerForm.reset();
            
            // Redirect to login page after 2 seconds
            setTimeout(() => {
                // window.location.href = 'login.html';
                console.log('Would redirect to login page');
            }, 2000);
        } else {
            showMessage(false, data.message || 'Registration failed. Please try again.');
        }
        
    } catch (error) {
        console.error('Registration error:', error);
        showMessage(false, 'Network error. Please check your connection and try again.');
    } finally {
        setLoading(false);
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('US-01 Registration page loaded');
    emailInput.focus();
});
